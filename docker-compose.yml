version: '3.8'

services:
  # MySQL Database Service
  mysql-db:
    image: mysql:8.0
    container_name: sales-management-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: sales_management
      MYSQL_USER: sales_user
      MYSQL_PASSWORD: sales_password
      MYSQL_ROOT_HOST: '%'
    ports:
      - "3307:3306"  # Map to 3307 to avoid conflicts with local MySQL
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf
    networks:
      - sales-network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "127.0.0.1", "-u", "sales_user", "-psales_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Spring Boot Backend Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sales-management-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_HOST: mysql-db
      DB_PORT: 3306
      DB_NAME: sales_management
      DB_USERNAME: sales_user
      DB_PASSWORD: sales_password
      JWT_SECRET: bXlTZWNyZXRLZXkxMjM0NTY3ODkwMTIzNDU2Nzg5MDEyMzQ1Njc4OTAxMjM0NTY3ODkw
      JWT_EXPIRATION: 86400000
      JAVA_OPTS: "-Xmx1g -Xms512m"
    ports:
      - "8081:8081"
    volumes:
      - ./logs:/app/logs
    networks:
      - sales-network
    depends_on:
      mysql-db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://127.0.0.1:8081/api/auth/test"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # Optional: phpMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: sales-management-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql-db
      PMA_PORT: 3306
      PMA_USER: sales_user
      PMA_PASSWORD: sales_password
      MYSQL_ROOT_PASSWORD: root_password
    ports:
      - "8080:80"
    networks:
      - sales-network
    depends_on:
      - mysql-db
    profiles:
      - tools  # Only start with --profile tools

# Named volumes for data persistence
volumes:
  mysql_data:
    driver: local

# Custom network for service communication
networks:
  sales-network:
    driver: bridge
