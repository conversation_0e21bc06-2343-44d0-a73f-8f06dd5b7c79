HELP.md
target/
.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Application specific ###
application-local.properties
application-dev.properties
application-prod.properties
.env
.env.local

### Test files ###
test_*.ps1
test_*.json
*.http

### Logs ###
logs/
*.log

### Database ###
*.db
*.sqlite

### Temporary files ###
*.tmp
*.temp

### Security ###
*.key
*.pem
*.p12
*.jks

### OS ###
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
