<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Sales Management System (Fixed)</title>
    <link rel="stylesheet" href="../css/admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Login-specific styles */
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-container {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
            margin: 1rem;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .login-header p {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .form-group {
            position: relative;
        }

        .form-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            z-index: 1;
        }

        .form-group input {
            padding-left: 3rem;
            height: 3rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all 0.2s;
        }

        .form-group input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        .login-btn {
            height: 3rem;
            font-size: 1rem;
            font-weight: 600;
            margin-top: 1rem;
        }

        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .login-footer p {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .error-message {
            background: rgb(220 38 38 / 0.1);
            border: 1px solid rgb(220 38 38 / 0.2);
            color: var(--danger-color);
            padding: 0.75rem;
            border-radius: var(--border-radius-sm);
            margin-bottom: 1rem;
            display: none;
            align-items: center;
            gap: 0.5rem;
        }

        .success-message {
            background: rgb(5 150 105 / 0.1);
            border: 1px solid rgb(5 150 105 / 0.2);
            color: var(--success-color);
            padding: 0.75rem;
            border-radius: var(--border-radius-sm);
            margin-bottom: 1rem;
            display: none;
            align-items: center;
            gap: 0.5rem;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-spinner {
            background: var(--bg-primary);
            padding: 2rem;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--text-primary);
        }

        .spinner {
            width: 24px;
            height: 24px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 1rem;
            font-size: 0.75rem;
            color: #6c757d;
        }

        /* Responsive adjustments */
        @media (max-width: 480px) {
            .login-container {
                margin: 0.5rem;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>
                <i class="fas fa-shield-alt"></i>
                Admin Login (Fixed)
            </h1>
            <p>Sales Management System Administration</p>
        </div>

        <div id="errorMessage" class="error-message">
            <i class="fas fa-exclamation-circle"></i>
            <span id="errorText">Invalid credentials</span>
        </div>

        <div id="successMessage" class="success-message">
            <i class="fas fa-check-circle"></i>
            <span id="successText">Login successful!</span>
        </div>

        <form id="loginForm" class="login-form">
            <div class="form-group">
                <i class="fas fa-user"></i>
                <input type="text" id="username" name="username" placeholder="Username" value="admin" required autocomplete="username">
            </div>

            <div class="form-group">
                <i class="fas fa-lock"></i>
                <input type="password" id="password" name="password" placeholder="Password" value="admin123" required autocomplete="current-password">
            </div>

            <button type="submit" class="btn btn-primary login-btn" id="loginBtn">
                <i class="fas fa-sign-in-alt"></i>
                Sign In
            </button>
        </form>

        <div class="login-footer">
            <p>
                <i class="fas fa-info-circle"></i>
                Admin access required for this area
            </p>
        </div>

        <div class="debug-info">
            <strong>Debug Info:</strong><br>
            Endpoint: <span id="debugEndpoint">http://localhost:8081/api/auth/login</span><br>
            Status: <span id="debugStatus">Ready</span>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <span>Authenticating...</span>
        </div>
    </div>

    <script>
        class FixedLoginManager {
            constructor() {
                this.baseUrl = window.location.origin;
                this.apiBase = '/api/auth';
                this.init();
            }

            init() {
                console.log('Fixed Login Manager initialized');
                console.log('Base URL:', this.baseUrl);
                console.log('API Base:', this.apiBase);
                
                // Update debug info
                document.getElementById('debugEndpoint').textContent = `${this.baseUrl}${this.apiBase}/login`;
                
                // Check if already authenticated
                this.checkExistingAuth();
                
                // Setup form submission
                const loginForm = document.getElementById('loginForm');
                if (loginForm) {
                    loginForm.addEventListener('submit', this.handleLogin.bind(this));
                }

                // Focus on username field
                const usernameField = document.getElementById('username');
                if (usernameField) {
                    usernameField.focus();
                }

                // Test endpoint availability
                this.testEndpoint();
            }

            async testEndpoint() {
                try {
                    const response = await fetch(`${this.baseUrl}${this.apiBase}/test`, {
                        method: 'GET'
                    });
                    
                    if (response.ok) {
                        document.getElementById('debugStatus').textContent = 'Backend Available ✅';
                        document.getElementById('debugStatus').style.color = 'green';
                    } else {
                        document.getElementById('debugStatus').textContent = `Backend Error (${response.status})`;
                        document.getElementById('debugStatus').style.color = 'orange';
                    }
                } catch (error) {
                    document.getElementById('debugStatus').textContent = 'Backend Unavailable ❌';
                    document.getElementById('debugStatus').style.color = 'red';
                    console.error('Backend test failed:', error);
                }
            }

            checkExistingAuth() {
                const token = localStorage.getItem('admin_jwt_token');
                const userInfo = localStorage.getItem('admin_user_info');
                
                if (token && userInfo) {
                    try {
                        const payload = JSON.parse(atob(token.split('.')[1]));
                        const user = JSON.parse(userInfo);
                        const currentTime = Date.now() / 1000;
                        
                        if (payload.exp > currentTime && user.role === 'ADMIN') {
                            console.log('Valid admin session found, redirecting...');
                            this.showSuccess('Valid session found, redirecting...');
                            setTimeout(() => {
                                window.location.href = '/admin/index.html';
                            }, 1000);
                            return;
                        }
                    } catch (error) {
                        console.log('Invalid stored auth data, clearing...');
                        this.clearAuth();
                    }
                }
            }

            async handleLogin(event) {
                event.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                if (!username || !password) {
                    this.showError('Please enter both username and password');
                    return;
                }

                try {
                    this.showLoading(true);
                    this.hideMessages();
                    
                    console.log('Attempting login with:', { username, password: '***' });
                    console.log('Request URL:', `${this.baseUrl}${this.apiBase}/login`);
                    
                    const requestBody = {
                        username: username,
                        password: password
                    };
                    
                    const response = await fetch(`${this.baseUrl}${this.apiBase}/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestBody)
                    });

                    console.log('Response status:', response.status);
                    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                    const responseText = await response.text();
                    console.log('Raw response:', responseText);

                    if (!response.ok) {
                        let errorMessage = 'Login failed';
                        try {
                            const errorData = JSON.parse(responseText);
                            errorMessage = errorData.message || errorMessage;
                        } catch (e) {
                            errorMessage = `HTTP ${response.status}: ${responseText.substring(0, 100)}`;
                        }
                        throw new Error(errorMessage);
                    }

                    const data = JSON.parse(responseText);
                    console.log('Parsed response:', data);
                    
                    // Store authentication data
                    if (data.accessToken) {
                        localStorage.setItem('admin_jwt_token', data.accessToken);
                        console.log('Access token stored');
                    }
                    
                    if (data.refreshToken) {
                        localStorage.setItem('admin_refresh_token', data.refreshToken);
                        console.log('Refresh token stored');
                    }
                    
                    // Extract and verify user info
                    let userInfo;
                    if (data.user) {
                        userInfo = {
                            username: data.user.username,
                            role: data.user.role,
                            email: data.user.email,
                            firstName: data.user.firstName,
                            lastName: data.user.lastName,
                            id: data.user.id
                        };
                    } else {
                        userInfo = this.extractUserInfoFromToken(data.accessToken);
                    }
                    
                    console.log('User info:', userInfo);
                    
                    if (!userInfo || userInfo.role !== 'ADMIN') {
                        throw new Error('Access denied. Admin privileges required.');
                    }
                    
                    localStorage.setItem('admin_user_info', JSON.stringify(userInfo));
                    console.log('User info stored');

                    this.showSuccess('Login successful! Redirecting...');
                    
                    // Redirect to admin interface
                    setTimeout(() => {
                        window.location.href = '/admin/index.html';
                    }, 1000);
                    
                } catch (error) {
                    console.error('Login error:', error);
                    this.showError(error.message);
                } finally {
                    this.showLoading(false);
                }
            }

            extractUserInfoFromToken(token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    return {
                        username: payload.sub,
                        role: payload.role || 'USER',
                        exp: payload.exp,
                        iat: payload.iat
                    };
                } catch (error) {
                    console.error('Error extracting user info from token:', error);
                    return null;
                }
            }

            clearAuth() {
                localStorage.removeItem('admin_jwt_token');
                localStorage.removeItem('admin_user_info');
                localStorage.removeItem('admin_refresh_token');
            }

            showError(message) {
                const errorElement = document.getElementById('errorMessage');
                const errorText = document.getElementById('errorText');
                
                if (errorElement && errorText) {
                    errorText.textContent = message;
                    errorElement.style.display = 'flex';
                }
            }

            showSuccess(message) {
                const successElement = document.getElementById('successMessage');
                const successText = document.getElementById('successText');
                
                if (successElement && successText) {
                    successText.textContent = message;
                    successElement.style.display = 'flex';
                }
            }

            hideMessages() {
                const errorElement = document.getElementById('errorMessage');
                const successElement = document.getElementById('successMessage');
                
                if (errorElement) errorElement.style.display = 'none';
                if (successElement) successElement.style.display = 'none';
            }

            showLoading(show) {
                const loadingOverlay = document.getElementById('loadingOverlay');
                const loginBtn = document.getElementById('loginBtn');
                
                if (loadingOverlay) {
                    loadingOverlay.style.display = show ? 'flex' : 'none';
                }
                
                if (loginBtn) {
                    loginBtn.disabled = show;
                    if (show) {
                        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
                    } else {
                        loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Sign In';
                    }
                }
            }
        }

        // Initialize login manager when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new FixedLoginManager();
        });
    </script>
</body>
</html>
