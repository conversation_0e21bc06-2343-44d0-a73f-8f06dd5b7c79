[mysqld]
# Basic settings
default_authentication_plugin = mysql_native_password
skip-host-cache
skip-name-resolve

# InnoDB settings
innodb_buffer_pool_size = 134217728
innodb_log_file_size = 50331648
innodb_log_buffer_size = 16777216
innodb_flush_log_at_trx_commit = 1
innodb_file_per_table = 1

# Connection settings
max_connections = 200
max_allowed_packet = 64M

# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Logging
general_log = 0
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Security
local_infile = 0

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
