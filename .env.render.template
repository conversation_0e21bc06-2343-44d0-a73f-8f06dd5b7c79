# Environment Variables Template for Render.com Deployment
# Copy this file to .env.render and fill in your actual values
# DO NOT commit the actual .env.render file to version control

# =============================================================================
# REQUIRED ENVIRONMENT VARIABLES
# =============================================================================

# Database Configuration (Choose ONE approach)
# Option 1: Complete DATABASE_URL (Render.com style)
# DATABASE_URL=mysql://username:password@host:port/database

# Option 2: Individual database variables (for local testing with H2)
DATABASE_URL=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
# DB_HOST=localhost
# DB_PORT=3306
# DB_NAME=sales_management
# DB_USERNAME=sa
# DB_PASSWORD=

# Security Configuration (REQUIRED)
JWT_SECRET=your-super-secure-jwt-secret-key-minimum-256-bits-long-replace-this
JWT_EXPIRATION=86400000

# =============================================================================
# OPTIONAL ENVIRONMENT VARIABLES
# =============================================================================

# Server Configuration
PORT=8081
SPRING_PROFILES_ACTIVE=render
CONTEXT_PATH=/

# CORS Configuration (adjust for your frontend domain)
CORS_ALLOWED_ORIGINS=https://your-frontend-domain.com,https://localhost:3000
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_MAX_AGE=3600

# Database Connection Pool Configuration
DB_POOL_MAX_SIZE=15
DB_POOL_MIN_IDLE=3
DB_POOL_CONNECTION_TIMEOUT=30000
DB_POOL_IDLE_TIMEOUT=300000
DB_POOL_MAX_LIFETIME=1200000
DB_POOL_LEAK_DETECTION=60000

# Database Behavior Configuration
# Use 'update' for cloud deployment, 'validate' for production with existing schema
DB_DDL_AUTO=update
DB_SHOW_SQL=false
DB_FORMAT_SQL=false
DB_BATCH_SIZE=20
DB_SSL=true
DB_REQUIRE_SSL=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_LEVEL_WEB=WARN
LOG_LEVEL_SQL=WARN
LOG_PATTERN_CONSOLE=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
LOG_PATTERN_FILE=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# File Upload Configuration
MAX_FILE_SIZE=500MB
MAX_REQUEST_SIZE=500MB
UPDATE_STORAGE_PATH=/tmp/versions
UPDATE_MAX_FILE_SIZE=524288000
UPDATE_ALLOWED_EXTENSIONS=jar
UPDATE_ENABLE_RESUMABLE=true
UPDATE_CLEANUP_ORPHANED=true

# Keep-Alive Service Configuration
# Prevents cloud service from sleeping due to inactivity (useful for free tiers)
KEEP_ALIVE_ENABLED=true
KEEP_ALIVE_URL=https://your-app-name.onrender.com
KEEP_ALIVE_INTERVAL=840000

# Update System Configuration
UPDATE_HEARTBEAT_INTERVAL=30000
UPDATE_CONNECTION_TIMEOUT=300000
UPDATE_ADMIN_ROLE=ADMIN
UPDATE_RATE_LIMIT=10

# JAR Validation Configuration
UPDATE_JAR_STRICT_MIME=true
UPDATE_JAR_REQUIRE_MANIFEST=false
UPDATE_JAR_MAX_ENTRIES=10000
UPDATE_JAR_MAX_MANIFEST_SIZE=65536

# JVM Configuration
JAVA_OPTS=-Xmx512m -Xms256m -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0

# =============================================================================
# LOCAL TESTING VARIABLES (for docker-compose.render.yml)
# =============================================================================

# Database root password (for local MySQL container)
DB_ROOT_PASSWORD=root_password

# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. JWT_SECRET should be at least 256 bits (32 characters) long
# 2. Use a strong, unique password for DB_PASSWORD
# 3. Never commit actual values to version control
# 4. Rotate secrets regularly
# 5. Use Render.com's environment variable encryption

# =============================================================================
# EXAMPLE VALUES (DO NOT USE IN PRODUCTION)
# =============================================================================

# Example DATABASE_URL for PlanetScale:
# DATABASE_URL=mysql://username:<EMAIL>/database?sslaccept=strict

# Example DATABASE_URL for AWS RDS:
# DATABASE_URL=mysql://username:<EMAIL>:3306/sales_management

# Example DATABASE_URL for Google Cloud SQL:
# DATABASE_URL=mysql://username:password@34.123.45.67:3306/sales_management

# Example strong JWT_SECRET (generate your own):
# JWT_SECRET=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2

# =============================================================================
# RENDER.COM SPECIFIC NOTES
# =============================================================================

# 1. Render.com automatically sets the PORT environment variable
# 2. Use HTTPS URLs for CORS_ALLOWED_ORIGINS in production
# 3. Render.com provides free SSL certificates
# 4. Database connections should use SSL in production
# 5. Consider using Render.com's PostgreSQL service as an alternative to MySQL
