 

  .   ____          _            __ _ _

 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \

( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \

 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )

  '  |____| .__|_| |_|_| |_\__, | / / / /

 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::               (v2.7.18)

 

2025-07-18 20:58:02 [main] INFO  c.h.s.SalesManagementBackendApplication - Starting SalesManagementBackendApplication v0.0.1-SNAPSHOT using Java 17.0.15 on 303fc740ac50 with PID 2 (/app/app.jar started by appuser in /app)

2025-07-18 20:58:02 [main] INFO  c.h.s.SalesManagementBackendApplication - The following 1 profile is active: "dev"

2025-07-18 20:58:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.

2025-07-18 20:58:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 289 ms. Found 23 JPA repository interfaces.

2025-07-18 20:58:09 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)

2025-07-18 20:58:09 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]

2025-07-18 20:58:09 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]

2025-07-18 20:58:10 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext

2025-07-18 20:58:10 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7079 ms

2025-07-18 20:58:11 [main] INFO  c.h.s.config.HibernateConfig - Customizing Hibernate properties for MySQL schema creation...

2025-07-18 20:58:11 [main] INFO  c.h.s.config.HibernateConfig - Hibernate properties customization completed

2025-07-18 20:58:11 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]

2025-07-18 20:58:11 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final

2025-07-18 20:58:12 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

2025-07-18 20:58:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...

2025-07-18 20:58:13 [main] WARN  o.h.e.j.e.i.JdbcEnvironmentInitiator - HHH000342: Could not obtain connection to query metadata

java.lang.RuntimeException: Driver com.mysql.cj.jdbc.Driver claims to not accept jdbcUrl, ${DATABASE_URL}

	at com.zaxxer.hikari.util.DriverDataSource.<init>(DriverDataSource.java:110)

	at com.zaxxer.hikari.pool.PoolBase.initializeDataSource(PoolBase.java:331)

	at com.zaxxer.hikari.pool.PoolBase.<init>(PoolBase.java:114)

	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:108)

	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:505)

	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)

	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)

	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:181)

