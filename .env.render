# Environment Variables for Render.com Deployment - Local Testing Configuration
# This file contains actual values for local testing

# =============================================================================
# REQUIRED ENVIRONMENT VARIABLES
# =============================================================================

# Database Configuration - Using H2 for local testing
DATABASE_URL=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE

# Security Configuration (REQUIRED)
JWT_SECRET=bXlTZWNyZXRLZXkxMjM0NTY3ODkwMTIzNDU2Nzg5MDEyMzQ1Njc4OTAxMjM0NTY3ODkw
JWT_EXPIRATION=86400000

# =============================================================================
# OPTIONAL ENVIRONMENT VARIABLES
# =============================================================================

# Server Configuration
PORT=8081
SPRING_PROFILES_ACTIVE=render
CONTEXT_PATH=/

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,https://localhost:3000
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_MAX_AGE=3600

# Database Connection Pool Configuration
DB_POOL_MAX_SIZE=15
DB_POOL_MIN_IDLE=3
DB_POOL_CONNECTION_TIMEOUT=30000
DB_POOL_IDLE_TIMEOUT=300000
DB_POOL_MAX_LIFETIME=1200000
DB_POOL_LEAK_DETECTION=60000

# Database Behavior Configuration
DB_DDL_AUTO=update
DB_SHOW_SQL=false
DB_FORMAT_SQL=false
DB_BATCH_SIZE=20
DB_SSL=false
DB_REQUIRE_SSL=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_LEVEL_WEB=WARN
LOG_LEVEL_SQL=WARN
LOG_PATTERN_CONSOLE=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
LOG_PATTERN_FILE=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# File Upload Configuration
MAX_FILE_SIZE=500MB
MAX_REQUEST_SIZE=500MB
UPDATE_STORAGE_PATH=/tmp/versions
UPDATE_MAX_FILE_SIZE=524288000
UPDATE_ALLOWED_EXTENSIONS=jar
UPDATE_ENABLE_RESUMABLE=true
UPDATE_CLEANUP_ORPHANED=true

# Keep-Alive Service Configuration
# Prevents Render.com service from sleeping due to inactivity
KEEP_ALIVE_ENABLED=true
KEEP_ALIVE_URL=https://sales-managment-system-backend-springboot.onrender.com
KEEP_ALIVE_INTERVAL=840000

# Update System Configuration
UPDATE_HEARTBEAT_INTERVAL=30000
UPDATE_CONNECTION_TIMEOUT=300000
UPDATE_ADMIN_ROLE=ADMIN
UPDATE_RATE_LIMIT=10

# JAR Validation Configuration
UPDATE_JAR_STRICT_MIME=true
UPDATE_JAR_REQUIRE_MANIFEST=false
UPDATE_JAR_MAX_ENTRIES=10000
UPDATE_JAR_MAX_MANIFEST_SIZE=65536

# JVM Configuration
JAVA_OPTS=-Xmx512m -Xms256m -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0

# =============================================================================
# LOCAL TESTING VARIABLES
# =============================================================================

# Database root password (for local MySQL container if needed)
DB_ROOT_PASSWORD=root_password
