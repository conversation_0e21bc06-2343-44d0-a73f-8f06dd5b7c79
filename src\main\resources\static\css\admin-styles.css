/* Admin Styles for Sales Management System */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;

    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;

    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* RTL Support Variables */
:root {
    --text-align-start: left;
    --text-align-end: right;
    --margin-start: margin-left;
    --margin-end: margin-right;
    --padding-start: padding-left;
    --padding-end: padding-right;
    --border-start: border-left;
    --border-end: border-right;
    --inset-start: left;
    --inset-end: right;
}

/* RTL Direction Support */
[dir="rtl"] {
    --text-align-start: right;
    --text-align-end: left;
    --margin-start: margin-right;
    --margin-end: margin-left;
    --padding-start: padding-right;
    --padding-end: padding-left;
    --border-start: border-right;
    --border-end: border-left;
    --inset-start: right;
    --inset-end: left;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Navigation */
.navbar {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
}

/* RTL Support for Navigation */
[dir="rtl"] .nav-container {
    flex-direction: row-reverse;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* RTL Support for Brand */
[dir="rtl"] .nav-brand {
    flex-direction: row-reverse;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* RTL Support for Menu */
[dir="rtl"] .nav-menu {
    flex-direction: row-reverse;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.page-header {
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-header p {
    color: var(--text-secondary);
    font-size: 1.125rem;
}

/* Cards */
.card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Forms */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* RTL Support for Forms */
[dir="rtl"] .form-group label {
    text-align: var(--text-align-start);
    flex-direction: row-reverse;
}

[dir="rtl"] .form-group input,
[dir="rtl"] .form-group textarea,
[dir="rtl"] .form-group select {
    text-align: var(--text-align-start);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Checkbox */
.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: border-color 0.2s, background-color 0.2s;
    cursor: pointer;
    position: relative;
}

.file-upload-area:hover,
.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgb(37 99 235 / 0.05);
}

.file-upload-content {
    pointer-events: none;
}

.file-upload-icon {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.file-upload-text {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.file-upload-link {
    color: var(--primary-color);
    font-weight: 500;
}

.file-upload-info {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.file-upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    margin-top: 0.5rem;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Progress Bar */
.progress-container {
    margin: 1rem 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    background: transparent;
}

/* RTL Support for Buttons */
[dir="rtl"] .btn {
    flex-direction: row-reverse;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #475569;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-outline {
    border-color: var(--border-color);
    color: var(--text-secondary);
}

.btn-outline:hover:not(:disabled) {
    background: var(--bg-tertiary);
}

.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-success {
    background: rgb(5 150 105 / 0.1);
    border: 1px solid rgb(5 150 105 / 0.2);
    color: var(--success-color);
}

.alert-error {
    background: rgb(220 38 38 / 0.1);
    border: 1px solid rgb(220 38 38 / 0.2);
    color: var(--danger-color);
}

.alert-warning {
    background: rgb(217 119 6 / 0.1);
    border: 1px solid rgb(217 119 6 / 0.2);
    color: var(--warning-color);
}

.alert-info {
    background: rgb(8 145 178 / 0.1);
    border: 1px solid rgb(8 145 178 / 0.2);
    color: var(--info-color);
}

/* Tables */
.table-container {
    overflow-x: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
}

.data-table tr:hover {
    background: var(--bg-secondary);
}

/* Filters */
.filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.filter-group label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.filter-group input,
.filter-group select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-active {
    background: rgb(5 150 105 / 0.1);
    color: var(--success-color);
}

.status-inactive {
    background: rgb(100 116 139 / 0.1);
    color: var(--secondary-color);
}

.status-mandatory {
    background: rgb(220 38 38 / 0.1);
    color: var(--danger-color);
}

.status-optional {
    background: rgb(8 145 178 / 0.1);
    color: var(--info-color);
}

/* Loading States */
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-muted);
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-number {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
}

.page-number:hover {
    background: var(--bg-tertiary);
}

.page-number.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-sm {
    max-width: 400px;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* RTL Support for Modal Header */
[dir="rtl"] .modal-header {
    flex-direction: row-reverse;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    text-align: var(--text-align-start);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0.25rem;
    order: 1;
}

/* RTL: Move close button to the left */
[dir="rtl"] .modal-close {
    order: -1;
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

/* RTL Support for Action Buttons */
[dir="rtl"] .action-buttons {
    flex-direction: row-reverse;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.action-view {
    background: var(--info-color);
    color: white;
}

.action-edit {
    background: var(--warning-color);
    color: white;
}

.action-delete {
    background: var(--danger-color);
    color: white;
}

.action-toggle {
    background: var(--secondary-color);
    color: white;
}

/* Version Details */
.version-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.detail-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.detail-group span {
    color: var(--text-secondary);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-muted {
    color: var(--text-muted);
}

.font-mono {
    font-family: 'Courier New', Courier, monospace;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .nav-container {
        padding: 0 0.5rem;
    }

    .container {
        padding: 1rem 0.5rem;
    }

    .card-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .form-actions {
        flex-direction: column;
    }

    .filters {
        flex-direction: column;
    }

    .pagination-container {
        flex-direction: column;
        gap: 1rem;
    }

    .data-table {
        font-size: 0.75rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }
}

/* ==================== RTL SUPPORT ==================== */

/* RTL Language Toggle Button */
.rtl-toggle {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1000;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

[dir="rtl"] .rtl-toggle {
    right: auto;
    left: 20px;
}

.rtl-toggle:hover {
    background: var(--primary-hover);
    transform: scale(1.1);
}

/* RTL Text Direction */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .text-center {
    text-align: center;
}

[dir="rtl"] .text-right {
    text-align: left;
}

/* RTL Modal Footer */
[dir="rtl"] .modal-footer {
    justify-content: flex-start;
    flex-direction: row-reverse;
}

/* RTL Card Header */
[dir="rtl"] .card-header {
    flex-direction: row-reverse;
}

/* RTL File Info */
[dir="rtl"] .file-details {
    flex-direction: row-reverse;
}

/* RTL Progress Text */
[dir="rtl"] .progress-text {
    flex-direction: row-reverse;
}

/* RTL Pagination */
[dir="rtl"] .pagination-container {
    flex-direction: row-reverse;
}

[dir="rtl"] .pagination-controls {
    flex-direction: row-reverse;
}

/* RTL User Info */
[dir="rtl"] .user-info {
    flex-direction: row-reverse;
}

/* RTL Form Actions */
[dir="rtl"] .form-actions {
    flex-direction: row-reverse;
}

/* RTL Filters */
[dir="rtl"] .filters {
    flex-direction: row-reverse;
}

/* RTL Alert */
[dir="rtl"] .alert {
    flex-direction: row-reverse;
}

/* RTL Checkbox */
[dir="rtl"] .checkbox-label {
    flex-direction: row-reverse;
}

/* RTL File Upload Area */
[dir="rtl"] .file-upload-area {
    text-align: center;
}

/* RTL Data Table */
[dir="rtl"] .data-table {
    direction: rtl;
}

[dir="rtl"] .data-table th,
[dir="rtl"] .data-table td {
    text-align: var(--text-align-start);
}

/* RTL Responsive Adjustments */
@media (max-width: 768px) {
    [dir="rtl"] .card-header {
        flex-direction: column-reverse;
        align-items: flex-end;
    }

    [dir="rtl"] .form-actions {
        flex-direction: column-reverse;
    }

    [dir="rtl"] .filters {
        flex-direction: column-reverse;
    }

    [dir="rtl"] .pagination-container {
        flex-direction: column-reverse;
    }

    [dir="rtl"] .action-buttons {
        flex-direction: column-reverse;
    }
}
